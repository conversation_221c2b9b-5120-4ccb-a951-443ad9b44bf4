import { AccountsAppEventType } from '../../services/accounts/types/AccountsAppEventType.js'
import { AdminAppEventType } from '../../services/admin/types/AdminAppEventType.js'
import { AssetsAppEventType } from '../../services/assets/types/AssetsAppEventType.js'
import { BaseAppEventType } from '../../services/appEvents/types/BaseAppEventType.js'
import { BgChannelsAppEventType } from '../../services/bgChannels/types/BgChannelsAppEventType.js'
import { ContentAppEventType } from '../../services/content/types/contentAppEventType.js'
import { ContentTagsAppEventType } from '../../services/contentTags/types/ContentTagsAppEventType.js'
import { DbAppEventType } from '../../services/db/types/DbAppEventType.js'
import { GroupsAppEventType } from '../../services/groups/types/GroupsAppEventType.js'
import { MatchingAppEventType } from '../../services/matching/types/MatchingAppEventType.js'
import { Mm2AppEventType } from '../../services/mm2/types/Mm2AppMessageType.js'
import { NatsAppEventType } from '../../services/nats/types/NatsAppEventType.js'
import { NlpAppEventType } from '../../services/nlp/types/NlpAppMessageType.js'
import { SecureIdAppEventType } from '../../services/secureId/types/SecureIdAppEventType.js'
import { VtsAppEventType } from '../../services/vts/types/VtsAppEventType.js'
import { WalletAppEventType } from '../../services/wallet/types/WalletAppEventType.js'

export const AppEventType = {
  appStarted: 'appEvents.appStarted',
  moderationEvent: 'appEvents.moderationEvent',
  ...AccountsAppEventType,
  ...AdminAppEventType,
  ...AssetsAppEventType,
  ...BaseAppEventType,
  ...BgChannelsAppEventType,
  ...ContentAppEventType,
  ...ContentTagsAppEventType,
  ...DbAppEventType,
  ...GroupsAppEventType,
  ...MatchingAppEventType,
  ...Mm2AppEventType,
  ...NatsAppEventType,
  ...NlpAppEventType,
  ...SecureIdAppEventType,
  ...VtsAppEventType,
  ...WalletAppEventType,
} as const

export type AppEventType = typeof AppEventType[keyof typeof AppEventType]
