export const NatsAppEventType = {
  /** NATS stream message received and bridged to GraphQL */
  natsStreamMessage: 'appEvents.natsStreamMessage',
  
  /** NATS connection status changed */
  natsConnectionChanged: 'appEvents.natsConnectionChanged',
  
  /** NATS consumer created or updated */
  natsConsumerChanged: 'appEvents.natsConsumerChanged',
  
  /** NATS stream created or updated */
  natsStreamChanged: 'appEvents.natsStreamChanged',
} as const

export type NatsAppEventType = typeof NatsAppEventType[keyof typeof NatsAppEventType]
