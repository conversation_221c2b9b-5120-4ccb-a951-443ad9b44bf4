# NATS Frontend Integration Guide

This guide explains how to use NATS messaging with frontend clients through GraphQL subscriptions.

## Overview

Since NATS is a server-side messaging system and browsers cannot directly connect to NATS servers, we use a bridge pattern:

1. **Backend**: NATS subscribers receive messages and forward them to GraphQL subscriptions
2. **Frontend**: Clients subscribe to GraphQL subscriptions via WebSocket to receive real-time messages

## Architecture

```
NATS Server → Backend NATS Subscriber → GraphQL PubSub → WebSocket → Frontend Client
```

## Setup Instructions

### 1. Configure NATS Service Plugin

Create or modify a service plugin to include NATS subscriptions that bridge to GraphQL:

```typescript
// In your service plugin file
import { frontendBridgePlugin } from '../services/nats/examples/frontendBridgePlugin.js'

export const yourServicePlugin = {
  // ... other plugin configuration
  nats: frontendBridgePlugin
}
```

### 2. Register the GraphQL Resolver

Add the NATS message resolver to your GraphQL schema:

```typescript
// In your GraphQL resolvers index
export { NatsMessageResolver } from './NatsMessageResolver.js'
```

### 3. Frontend Implementation Options

#### Option A: Use the Example HTML Client

1. Open `http://localhost:8092/fsdata/nats-frontend-example.html`
2. Enter a subject pattern (e.g., `frontend.*` or `.*` for all)
3. Click "Connect" to start receiving messages
4. Use the test publisher to send messages

#### Option B: JavaScript/TypeScript Client

```javascript
// WebSocket GraphQL client for NATS messages
class NatsClient {
  constructor(wsUrl) {
    this.ws = new WebSocket(wsUrl, 'graphql-ws')
    this.setupEventHandlers()
  }

  subscribe(subjectPattern = '.*') {
    const subscription = {
      id: this.generateId(),
      type: 'start',
      payload: {
        query: `
          subscription NatsMessages($subjectPattern: String) {
            natsMessages(subjectPattern: $subjectPattern) {
              message
              subject
              data
              natsMetadata {
                subject
                sequence
                timestamp
                streamName
              }
            }
          }
        `,
        variables: { subjectPattern }
      }
    }
    this.ws.send(JSON.stringify(subscription))
  }

  setupEventHandlers() {
    this.ws.onopen = () => {
      this.ws.send(JSON.stringify({
        type: 'connection_init',
        payload: {}
      }))
    }

    this.ws.onmessage = (event) => {
      const message = JSON.parse(event.data)
      if (message.type === 'next' && message.payload?.data?.natsMessages) {
        this.handleNatsMessage(message.payload.data.natsMessages)
      }
    }
  }

  handleNatsMessage(natsMessage) {
    console.log('Received NATS message:', natsMessage)
    // Handle the message in your application
  }
}

// Usage
const client = new NatsClient('ws://localhost:8092/fsdata/api/graphql')
client.subscribe('frontend.*') // Subscribe to frontend messages
```

#### Option C: React Hook

```typescript
import { useSubscription } from '@apollo/client'
import { gql } from '@apollo/client'

const NATS_MESSAGES_SUBSCRIPTION = gql`
  subscription NatsMessages($subjectPattern: String) {
    natsMessages(subjectPattern: $subjectPattern) {
      message
      subject
      data
      natsMetadata {
        subject
        sequence
        timestamp
        streamName
      }
    }
  }
`

export const useNatsMessages = (subjectPattern = '.*') => {
  const { data, loading, error } = useSubscription(NATS_MESSAGES_SUBSCRIPTION, {
    variables: { subjectPattern }
  })

  return {
    message: data?.natsMessages,
    loading,
    error
  }
}

// Usage in component
function MyComponent() {
  const { message, loading, error } = useNatsMessages('frontend.notifications')

  useEffect(() => {
    if (message) {
      console.log('New NATS message:', message)
      // Handle the message
    }
  }, [message])

  return <div>...</div>
}
```

## Testing

### 1. Start the Server

Make sure your server is running with NATS enabled:

```bash
npm start
```

### 2. Publish Test Messages

Use the test publisher to send messages:

```bash
# Publish once
node src/services/nats/examples/testPublisher.ts once

# Auto-publish every 10 seconds
node src/services/nats/examples/testPublisher.ts auto

# Custom message
node src/services/nats/examples/testPublisher.ts custom frontend.alerts "Test alert"
```

### 3. Monitor Messages

- Open the example HTML client: `http://localhost:8092/fsdata/nats-frontend-example.html`
- Or use GraphQL Playground: `http://localhost:8092/fsdata/api/graphql`

## Message Format

Messages received by the frontend will have this structure:

```typescript
interface NatsMessage {
  message: string        // The main message content
  subject: string        // NATS subject
  data?: string         // Raw message data (if different from message)
  natsMetadata: {
    subject: string      // NATS subject
    sequence: number     // Message sequence number
    timestamp: string    // When received
    streamName?: string  // NATS stream name
  }
}
```

## Subject Patterns

You can filter messages by subject using regex patterns:

- `.*` - All messages
- `frontend.*` - All frontend messages
- `frontend\.notifications` - Only notification messages
- `user\.123\..*` - All messages for user 123

## Production Considerations

1. **Rate Limiting**: The GraphQL subscription has rate limiting (100 requests/minute)
2. **Authentication**: Add authentication to GraphQL subscriptions for production
3. **Message Filtering**: Use subject patterns to reduce unnecessary traffic
4. **Error Handling**: Implement proper error handling and reconnection logic
5. **Monitoring**: Monitor WebSocket connections and message throughput

## Troubleshooting

### Common Issues

1. **WebSocket Connection Failed**
   - Check if the server is running
   - Verify the WebSocket URL
   - Check for CORS issues

2. **No Messages Received**
   - Verify NATS service is running
   - Check if messages are being published to the correct subjects
   - Verify the subject pattern matches

3. **GraphQL Subscription Errors**
   - Check if the NatsMessageResolver is registered
   - Verify the GraphQL schema includes the subscription
   - Check server logs for errors

### Debug Commands

```bash
# Check NATS connection
curl http://localhost:8092/fsdata/status/med

# Test GraphQL subscription manually
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"query":"subscription { natsMessages { message subject } }"}' \
  http://localhost:8092/fsdata/api/graphql
```

## Next Steps

1. Implement authentication for production use
2. Add message persistence and replay capabilities
3. Create typed message interfaces for your specific use cases
4. Add monitoring and alerting for message delivery
5. Consider implementing message acknowledgment from frontend clients
