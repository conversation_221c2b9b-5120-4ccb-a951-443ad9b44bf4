import * as jetstream from '@nats-io/jetstream'
import appHelpers from '../../../app/helpers.js'
import { AppEventType } from '../../../app/types/AppEventType.js'
import logger from '../../logger/index.js'

/**
 * Bridge function to forward NATS messages to GraphQL subscriptions
 * This allows frontend clients to receive NATS messages via GraphQL subscriptions
 */
export const createNatsToGraphqlBridge = (eventType: AppEventType = AppEventType.natsStreamMessage) => {
  return async (message: jetstream.JsMsg): Promise<void> => {
    try {
      // Decode the message data
      const data = new TextDecoder().decode(message.data)
      let payload: any

      try {
        payload = JSON.parse(data)
      } catch (parseError) {
        // If it's not JSON, treat as plain text
        payload = { message: data, subject: message.subject }
      }

      // Add metadata about the NATS message
      const enrichedPayload = {
        ...payload,
        natsMetadata: {
          subject: message.subject,
          sequence: message.seq,
          timestamp: new Date().toISOString(),
          streamName: message.info?.stream,
        }
      }

      // Publish to GraphQL subscription
      await appHelpers.publishAppEvent(eventType, enrichedPayload)

      // Acknowledge the message
      message.ack()

      logger.trace('NATS message bridged to GraphQL subscription', {
        subject: message.subject,
        eventType,
        sequence: message.seq
      })

    } catch (error) {
      logger.error('Error bridging NATS message to GraphQL', {
        error,
        subject: message.subject,
        eventType
      })

      // Negative acknowledge to retry later
      message.nak()
    }
  }
}

/**
 * Create a simple message processor that just logs and acknowledges
 */
export const createSimpleNatsProcessor = (logPrefix: string = 'NATS') => {
  return async (message: jetstream.JsMsg): Promise<void> => {
    try {
      const data = new TextDecoder().decode(message.data)
      logger.info(`${logPrefix} message received`, {
        subject: message.subject,
        data: data.substring(0, 200), // Log first 200 chars
        sequence: message.seq
      })

      message.ack()
    } catch (error) {
      logger.error(`${logPrefix} message processing error`, {
        error,
        subject: message.subject
      })
      message.nak()
    }
  }
}
