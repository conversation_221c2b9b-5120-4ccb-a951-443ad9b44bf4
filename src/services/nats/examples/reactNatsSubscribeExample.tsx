import React, { useState } from 'react'
import { useMutation, gql } from '@apollo/client'

// GraphQL mutations
const NATS_SUBSCRIBE_MUTATION = gql`
  mutation CreateNatsSubscription($input: NatsSubscribeInput!) {
    natsSubscribe(input: $input) {
      success
      message
      subscriptionId
      streamName
      subject
      consumerName
    }
  }
`

const NATS_PUBLISH_MUTATION = gql`
  mutation PublishNatsMessage($subject: String!, $message: String!, $headers: String) {
    natsPublish(subject: $subject, message: $message, headers: $headers) {
      success
      message
      sequence
      stream
    }
  }
`

// TypeScript interfaces
interface NatsSubscribeInput {
  streamName: string
  subject?: string
  consumerName: string
  bridgeToGraphQL?: boolean
  options?: {
    maxMessages?: number
    expires?: number
    consumerConfig?: {
      durable_name?: string
      deliver_policy?: string
      ack_policy?: string
      max_deliver?: number
    }
  }
}

interface NatsSubscribeResult {
  success: boolean
  message?: string
  subscriptionId?: string
  streamName?: string
  subject?: string
  consumerName?: string
}

interface NatsPublishResult {
  success: boolean
  message?: string
  sequence?: number
  stream?: string
}

export const NatsSubscribeComponent: React.FC = () => {
  // State for subscription form
  const [subscribeForm, setSubscribeForm] = useState({
    streamName: 'FRONTEND_MESSAGES',
    subject: 'frontend.notifications',
    consumerName: 'frontend-consumer-1',
    bridgeToGraphQL: true,
    maxMessages: 100,
    durableName: 'frontend-durable'
  })

  // State for publish form
  const [publishForm, setPublishForm] = useState({
    subject: 'frontend.notifications',
    message: JSON.stringify({
      type: 'notification',
      title: 'Test Message',
      content: 'This is a test message from React',
      timestamp: new Date().toISOString(),
      userId: 'test-user'
    }, null, 2),
    headers: JSON.stringify({ priority: 'high', source: 'react' }, null, 2)
  })

  // GraphQL mutations
  const [natsSubscribe, { loading: subscribeLoading, data: subscribeData, error: subscribeError }] = 
    useMutation<{ natsSubscribe: NatsSubscribeResult }>(NATS_SUBSCRIBE_MUTATION)

  const [natsPublish, { loading: publishLoading, data: publishData, error: publishError }] = 
    useMutation<{ natsPublish: NatsPublishResult }>(NATS_PUBLISH_MUTATION)

  // Handle subscribe form submission
  const handleSubscribe = async (e: React.FormEvent) => {
    e.preventDefault()
    
    const input: NatsSubscribeInput = {
      streamName: subscribeForm.streamName,
      subject: subscribeForm.subject || undefined,
      consumerName: subscribeForm.consumerName,
      bridgeToGraphQL: subscribeForm.bridgeToGraphQL,
      options: {
        maxMessages: subscribeForm.maxMessages,
        consumerConfig: {
          durable_name: subscribeForm.durableName,
          deliver_policy: 'new',
          ack_policy: 'explicit'
        }
      }
    }

    try {
      await natsSubscribe({ variables: { input } })
    } catch (error) {
      console.error('Error creating NATS subscription:', error)
    }
  }

  // Handle publish form submission
  const handlePublish = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      await natsPublish({
        variables: {
          subject: publishForm.subject,
          message: publishForm.message,
          headers: publishForm.headers || undefined
        }
      })
    } catch (error) {
      console.error('Error publishing NATS message:', error)
    }
  }

  return (
    <div style={{ maxWidth: '800px', margin: '0 auto', padding: '20px' }}>
      <h1>NATS Operations via GraphQL</h1>
      
      {/* Subscribe Section */}
      <div style={{ marginBottom: '40px', padding: '20px', border: '1px solid #ddd', borderRadius: '8px' }}>
        <h2>Create NATS Subscription</h2>
        <form onSubmit={handleSubscribe}>
          <div style={{ marginBottom: '15px' }}>
            <label>Stream Name:</label>
            <input
              type="text"
              value={subscribeForm.streamName}
              onChange={(e) => setSubscribeForm({ ...subscribeForm, streamName: e.target.value })}
              style={{ width: '100%', padding: '8px', marginTop: '5px' }}
              required
            />
          </div>
          
          <div style={{ marginBottom: '15px' }}>
            <label>Subject (optional):</label>
            <input
              type="text"
              value={subscribeForm.subject}
              onChange={(e) => setSubscribeForm({ ...subscribeForm, subject: e.target.value })}
              style={{ width: '100%', padding: '8px', marginTop: '5px' }}
              placeholder="e.g., frontend.notifications"
            />
          </div>
          
          <div style={{ marginBottom: '15px' }}>
            <label>Consumer Name:</label>
            <input
              type="text"
              value={subscribeForm.consumerName}
              onChange={(e) => setSubscribeForm({ ...subscribeForm, consumerName: e.target.value })}
              style={{ width: '100%', padding: '8px', marginTop: '5px' }}
              required
            />
          </div>
          
          <div style={{ marginBottom: '15px' }}>
            <label>
              <input
                type="checkbox"
                checked={subscribeForm.bridgeToGraphQL}
                onChange={(e) => setSubscribeForm({ ...subscribeForm, bridgeToGraphQL: e.target.checked })}
              />
              Bridge to GraphQL Subscriptions
            </label>
          </div>
          
          <div style={{ marginBottom: '15px' }}>
            <label>Max Messages:</label>
            <input
              type="number"
              value={subscribeForm.maxMessages}
              onChange={(e) => setSubscribeForm({ ...subscribeForm, maxMessages: parseInt(e.target.value) })}
              style={{ width: '100%', padding: '8px', marginTop: '5px' }}
            />
          </div>
          
          <div style={{ marginBottom: '15px' }}>
            <label>Durable Name:</label>
            <input
              type="text"
              value={subscribeForm.durableName}
              onChange={(e) => setSubscribeForm({ ...subscribeForm, durableName: e.target.value })}
              style={{ width: '100%', padding: '8px', marginTop: '5px' }}
            />
          </div>
          
          <button 
            type="submit" 
            disabled={subscribeLoading}
            style={{ padding: '10px 20px', backgroundColor: '#2196f3', color: 'white', border: 'none', borderRadius: '4px' }}
          >
            {subscribeLoading ? 'Creating...' : 'Create Subscription'}
          </button>
        </form>
        
        {/* Subscribe Results */}
        {subscribeData?.natsSubscribe && (
          <div style={{ 
            marginTop: '15px', 
            padding: '10px', 
            backgroundColor: subscribeData.natsSubscribe.success ? '#d4edda' : '#f8d7da',
            border: `1px solid ${subscribeData.natsSubscribe.success ? '#c3e6cb' : '#f5c6cb'}`,
            borderRadius: '4px'
          }}>
            <strong>{subscribeData.natsSubscribe.success ? '✓ Success' : '✗ Error'}:</strong>
            <br />
            {subscribeData.natsSubscribe.message}
            {subscribeData.natsSubscribe.success && (
              <>
                <br />
                <strong>Subscription ID:</strong> {subscribeData.natsSubscribe.subscriptionId}
                <br />
                <strong>Stream:</strong> {subscribeData.natsSubscribe.streamName}
                <br />
                <strong>Subject:</strong> {subscribeData.natsSubscribe.subject || 'All subjects'}
                <br />
                <strong>Consumer:</strong> {subscribeData.natsSubscribe.consumerName}
              </>
            )}
          </div>
        )}
        
        {subscribeError && (
          <div style={{ marginTop: '15px', padding: '10px', backgroundColor: '#f8d7da', border: '1px solid #f5c6cb', borderRadius: '4px' }}>
            <strong>✗ Error:</strong> {subscribeError.message}
          </div>
        )}
      </div>

      {/* Publish Section */}
      <div style={{ padding: '20px', border: '1px solid #ddd', borderRadius: '8px' }}>
        <h2>Publish NATS Message</h2>
        <form onSubmit={handlePublish}>
          <div style={{ marginBottom: '15px' }}>
            <label>Subject:</label>
            <input
              type="text"
              value={publishForm.subject}
              onChange={(e) => setPublishForm({ ...publishForm, subject: e.target.value })}
              style={{ width: '100%', padding: '8px', marginTop: '5px' }}
              required
            />
          </div>
          
          <div style={{ marginBottom: '15px' }}>
            <label>Message (JSON or plain text):</label>
            <textarea
              value={publishForm.message}
              onChange={(e) => setPublishForm({ ...publishForm, message: e.target.value })}
              style={{ width: '100%', padding: '8px', marginTop: '5px', minHeight: '120px' }}
              required
            />
          </div>
          
          <div style={{ marginBottom: '15px' }}>
            <label>Headers (JSON, optional):</label>
            <textarea
              value={publishForm.headers}
              onChange={(e) => setPublishForm({ ...publishForm, headers: e.target.value })}
              style={{ width: '100%', padding: '8px', marginTop: '5px', minHeight: '60px' }}
              placeholder='{"priority": "high", "source": "react"}'
            />
          </div>
          
          <button 
            type="submit" 
            disabled={publishLoading}
            style={{ padding: '10px 20px', backgroundColor: '#2196f3', color: 'white', border: 'none', borderRadius: '4px' }}
          >
            {publishLoading ? 'Publishing...' : 'Publish Message'}
          </button>
        </form>
        
        {/* Publish Results */}
        {publishData?.natsPublish && (
          <div style={{ 
            marginTop: '15px', 
            padding: '10px', 
            backgroundColor: publishData.natsPublish.success ? '#d4edda' : '#f8d7da',
            border: `1px solid ${publishData.natsPublish.success ? '#c3e6cb' : '#f5c6cb'}`,
            borderRadius: '4px'
          }}>
            <strong>{publishData.natsPublish.success ? '✓ Success' : '✗ Error'}:</strong>
            <br />
            {publishData.natsPublish.message}
            {publishData.natsPublish.success && (
              <>
                <br />
                <strong>Sequence:</strong> {publishData.natsPublish.sequence}
                <br />
                <strong>Stream:</strong> {publishData.natsPublish.stream}
              </>
            )}
          </div>
        )}
        
        {publishError && (
          <div style={{ marginTop: '15px', padding: '10px', backgroundColor: '#f8d7da', border: '1px solid #f5c6cb', borderRadius: '4px' }}>
            <strong>✗ Error:</strong> {publishError.message}
          </div>
        )}
      </div>
    </div>
  )
}

export default NatsSubscribeComponent
