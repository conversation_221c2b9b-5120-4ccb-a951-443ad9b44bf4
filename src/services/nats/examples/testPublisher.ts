#!/usr/bin/env node

/**
 * Test script to publish messages to NATS for frontend testing
 * Run this script to send test messages that will be received by the frontend
 */

import { publish } from '../operations/publish.js'
import logger from '../../logger/index.js'

interface TestMessage {
  id: string
  type: string
  content: string
  timestamp: string
  userId?: string
}

const createTestMessage = (type: string, content: string): TestMessage => ({
  id: Math.random().toString(36).substr(2, 9),
  type,
  content,
  timestamp: new Date().toISOString(),
  userId: 'test-user-123'
})

const publishTestMessages = async () => {
  try {
    console.log('Publishing test messages to NATS...')

    // Test notification message
    const notification = createTestMessage('notification', 'You have a new message!')
    await publish('frontend.notifications', notification)
    console.log('✓ Published notification message')

    // Test update message
    const update = createTestMessage('update', 'Your profile has been updated')
    await publish('frontend.updates', update)
    console.log('✓ Published update message')

    // Test alert message
    const alert = createTestMessage('alert', 'System maintenance in 10 minutes')
    await publish('frontend.alerts', alert)
    console.log('✓ Published alert message')

    // Test with simple string message
    await publish('frontend.notifications', 'Simple string message')
    console.log('✓ Published simple string message')

    // Test with complex data
    const complexData = {
      event: 'user_action',
      data: {
        action: 'login',
        user: {
          id: 'user-456',
          name: 'John Doe',
          email: '<EMAIL>'
        },
        metadata: {
          ip: '***********',
          userAgent: 'Mozilla/5.0...',
          timestamp: new Date().toISOString()
        }
      }
    }
    await publish('frontend.updates', complexData)
    console.log('✓ Published complex data message')

    console.log('\nAll test messages published successfully!')
    console.log('Check your frontend client to see the messages.')

  } catch (error) {
    logger.error('Error publishing test messages:', { error })
    console.error('Failed to publish test messages:', error)
  }
}

// Auto-publish messages every 10 seconds for testing
const startAutoPublisher = () => {
  const messages = [
    { subject: 'frontend.notifications', content: 'Auto notification' },
    { subject: 'frontend.updates', content: 'Auto update' },
    { subject: 'frontend.alerts', content: 'Auto alert' }
  ]

  let counter = 0
  setInterval(async () => {
    try {
      const message = messages[counter % messages.length]
      const testMsg = createTestMessage('auto', `${message.content} #${counter + 1}`)
      await publish(message.subject, testMsg)
      console.log(`Auto-published: ${message.subject} - ${testMsg.content}`)
      counter++
    } catch (error) {
      console.error('Auto-publish error:', error)
    }
  }, 10000) // Every 10 seconds
}

// Command line interface
const args = process.argv.slice(2)
const command = args[0]

switch (command) {
  case 'once':
    publishTestMessages()
    break
  
  case 'auto':
    console.log('Starting auto-publisher (every 10 seconds)...')
    console.log('Press Ctrl+C to stop')
    startAutoPublisher()
    break
  
  case 'custom':
    const subject = args[1] || 'frontend.notifications'
    const message = args[2] || 'Custom test message'
    publish(subject, createTestMessage('custom', message))
      .then(() => console.log(`Published to ${subject}: ${message}`))
      .catch(error => console.error('Error:', error))
    break
  
  default:
    console.log('NATS Test Publisher')
    console.log('Usage:')
    console.log('  npm run nats:test once     - Publish test messages once')
    console.log('  npm run nats:test auto     - Auto-publish every 10 seconds')
    console.log('  npm run nats:test custom <subject> <message> - Publish custom message')
    console.log('')
    console.log('Examples:')
    console.log('  npm run nats:test custom frontend.alerts "Server will restart"')
    console.log('  npm run nats:test custom test.frontend "Hello World"')
    break
}

export { publishTestMessages, startAutoPublisher }
