#!/usr/bin/env node

/**
 * Example script showing how to set up NATS stream subscriptions for frontend use
 * This demonstrates the complete flow from NATS messages to GraphQL subscriptions
 */

import { NatsServicePlugin } from '../types/NatsServicePlugin.js'
import { NatsStreamName } from '../../../app/types/NatsStreamName.js'
import { NatsSubject } from '../../../app/types/NatsSubject.js'
import { createNatsToGraphqlBridge } from '../helpers/natsToGraphqlBridge.js'
import { AppEventType } from '../../../app/types/AppEventType.js'
import { publish } from '../operations/publish.js'
import logger from '../../logger/index.js'

/**
 * Example NATS service plugin for frontend streaming
 */
export const exampleStreamPlugin: NatsServicePlugin = {
  // Define the streams we want to create
  streams: [
    {
      config: {
        name: 'USER_NOTIFICATIONS',
        subjects: ['user.*.notifications', 'user.*.alerts'],
        retention: 'limits',
        max_msgs: 10000,
        max_age: 7 * 24 * 60 * 60 * 1000000000, // 7 days in nanoseconds
        storage: 'file', // Persistent storage
        replicas: 1,
      }
    },
    {
      config: {
        name: 'SYSTEM_EVENTS',
        subjects: ['system.>'],
        retention: 'limits',
        max_msgs: 5000,
        max_age: 24 * 60 * 60 * 1000000000, // 24 hours
        storage: 'memory', // In-memory for faster access
      }
    },
    {
      config: {
        name: 'REAL_TIME_DATA',
        subjects: ['data.>'],
        retention: 'limits',
        max_msgs: 1000,
        max_age: 60 * 60 * 1000000000, // 1 hour
        storage: 'memory',
      }
    }
  ],

  // Define subscriptions that bridge to GraphQL
  subscriptions: [
    // User notifications
    {
      streamName: 'USER_NOTIFICATIONS' as NatsStreamName,
      subjectName: 'user.*.notifications' as NatsSubject,
      processor: createNatsToGraphqlBridge(AppEventType.natsStreamMessage),
      options: {
        maxMessages: 100,
        consumerConfig: {
          durable_name: 'user-notifications-graphql-bridge',
          deliver_policy: 'new',
          ack_policy: 'explicit',
          max_deliver: 3,
          ack_wait: 30000000000, // 30 seconds in nanoseconds
        }
      }
    },
    
    // User alerts (high priority)
    {
      streamName: 'USER_NOTIFICATIONS' as NatsStreamName,
      subjectName: 'user.*.alerts' as NatsSubject,
      processor: createNatsToGraphqlBridge(AppEventType.natsStreamMessage),
      options: {
        maxMessages: 50,
        consumerConfig: {
          durable_name: 'user-alerts-graphql-bridge',
          deliver_policy: 'new',
          ack_policy: 'explicit',
          max_deliver: 5, // Retry more for alerts
          ack_wait: 10000000000, // 10 seconds for faster processing
        }
      }
    },

    // System events
    {
      streamName: 'SYSTEM_EVENTS' as NatsStreamName,
      subjectName: 'system.status' as NatsSubject,
      processor: createNatsToGraphqlBridge(AppEventType.natsStreamMessage),
      options: {
        maxMessages: 20,
        consumerConfig: {
          durable_name: 'system-status-graphql-bridge',
          deliver_policy: 'new',
          ack_policy: 'explicit',
        }
      }
    },

    // Real-time data updates
    {
      streamName: 'REAL_TIME_DATA' as NatsStreamName,
      subjectName: 'data.metrics' as NatsSubject,
      processor: createNatsToGraphqlBridge(AppEventType.natsStreamMessage),
      options: {
        maxMessages: 200,
        consumerConfig: {
          durable_name: 'data-metrics-graphql-bridge',
          deliver_policy: 'new',
          ack_policy: 'explicit',
          max_deliver: 1, // Don't retry data updates
        }
      }
    }
  ]
}

/**
 * Example function to publish test messages to different streams
 */
export const publishTestMessages = async () => {
  const testMessages = [
    // User notifications
    {
      subject: 'user.123.notifications',
      data: {
        type: 'message',
        title: 'New Message',
        content: 'You have received a new message from John Doe',
        userId: '123',
        timestamp: new Date().toISOString(),
        priority: 'normal'
      }
    },
    
    // User alerts
    {
      subject: 'user.123.alerts',
      data: {
        type: 'security',
        title: 'Security Alert',
        content: 'Unusual login activity detected',
        userId: '123',
        timestamp: new Date().toISOString(),
        priority: 'high'
      }
    },

    // System status
    {
      subject: 'system.status',
      data: {
        type: 'health_check',
        service: 'api',
        status: 'healthy',
        timestamp: new Date().toISOString(),
        metrics: {
          cpu: 45.2,
          memory: 67.8,
          requests_per_second: 150
        }
      }
    },

    // Real-time data
    {
      subject: 'data.metrics',
      data: {
        type: 'user_activity',
        active_users: 1247,
        new_signups: 23,
        timestamp: new Date().toISOString(),
        region: 'us-west-2'
      }
    }
  ]

  for (const msg of testMessages) {
    try {
      await new Promise<void>((resolve, reject) => {
        publish(msg.subject, msg.data, undefined, (error) => {
          if (error) {
            reject(error)
          } else {
            console.log(`✓ Published to ${msg.subject}`)
            resolve()
          }
        })
      })
    } catch (error) {
      console.error(`✗ Failed to publish to ${msg.subject}:`, error)
    }
  }
}

/**
 * Example GraphQL subscription queries for frontend clients
 */
export const exampleGraphQLQueries = {
  // Subscribe to all user notifications for a specific user
  userNotifications: `
    subscription UserNotifications($userId: String!) {
      natsStreamMessages(
        streamName: "USER_NOTIFICATIONS"
        subjectPattern: "user\\.${userId}\\.notifications"
      ) {
        id
        subject
        message
        data
        natsMetadata {
          subject
          sequence
          timestamp
          streamName
          consumerName
        }
        receivedAt
      }
    }
  `,

  // Subscribe to high-priority alerts
  userAlerts: `
    subscription UserAlerts($userId: String!) {
      natsStreamMessages(
        streamName: "USER_NOTIFICATIONS"
        subjectPattern: "user\\.${userId}\\.alerts"
      ) {
        id
        subject
        message
        data
        natsMetadata {
          subject
          sequence
          timestamp
          streamName
        }
        receivedAt
      }
    }
  `,

  // Subscribe to system status updates
  systemStatus: `
    subscription SystemStatus {
      natsStreamMessages(
        streamName: "SYSTEM_EVENTS"
        subjectPattern: "system\\.status"
      ) {
        id
        subject
        message
        data
        receivedAt
      }
    }
  `,

  // Subscribe to real-time metrics
  realTimeMetrics: `
    subscription RealTimeMetrics {
      natsStreamMessages(
        streamName: "REAL_TIME_DATA"
        subjectPattern: "data\\.metrics"
      ) {
        id
        subject
        message
        data
        receivedAt
      }
    }
  `,

  // Subscribe to all messages (for debugging)
  allMessages: `
    subscription AllNatsMessages {
      natsStreamMessages {
        id
        subject
        message
        data
        natsMetadata {
          subject
          sequence
          timestamp
          streamName
          consumerName
        }
        receivedAt
      }
    }
  `
}

// CLI interface
if (require.main === module) {
  const command = process.argv[2]
  
  switch (command) {
    case 'publish':
      publishTestMessages()
        .then(() => console.log('All test messages published'))
        .catch(error => console.error('Error publishing messages:', error))
      break
    
    case 'queries':
      console.log('Example GraphQL Subscription Queries:')
      Object.entries(exampleGraphQLQueries).forEach(([name, query]) => {
        console.log(`\n--- ${name} ---`)
        console.log(query)
      })
      break
    
    default:
      console.log('NATS Stream Subscription Setup')
      console.log('Usage:')
      console.log('  npm run nats:setup publish  - Publish test messages')
      console.log('  npm run nats:setup queries  - Show example GraphQL queries')
      break
  }
}
