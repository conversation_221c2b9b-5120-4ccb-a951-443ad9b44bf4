import { NatsServicePlugin } from '../types/NatsServicePlugin.js'
import { NatsStreamName } from '../../../app/types/NatsStreamName.js'
import { NatsSubject } from '../../../app/types/NatsSubject.js'
import { createNatsToGraphqlBridge, createSimpleNatsProcessor } from '../helpers/natsToGraphqlBridge.js'
import { AppEventType } from '../../../app/types/AppEventType.js'

/**
 * Example NATS service plugin that bridges NATS messages to GraphQL subscriptions
 * This allows frontend clients to receive NATS messages in real-time
 */
export const frontendBridgePlugin: NatsServicePlugin = {
  // Define streams that this plugin will use
  streams: [
    {
      config: {
        name: 'FRONTEND_MESSAGES',
        subjects: ['frontend.>'], // All subjects starting with 'frontend.'
        retention: 'limits',
        max_msgs: 1000,
        max_age: 24 * 60 * 60 * 1000000000, // 24 hours in nanoseconds
        storage: 'memory',
      }
    }
  ],

  // Define subscriptions that will forward messages to GraphQL
  subscriptions: [
    {
      streamName: 'FRONTEND_MESSAGES' as NatsStreamName,
      subjectName: 'frontend.notifications' as NatsSubject,
      processor: createNatsToGraphqlBridge(AppEventType.natsStreamMessage),
      options: {
        maxMessages: 100,
        consumerConfig: {
          durable_name: 'frontend-notifications-consumer',
          deliver_policy: 'new',
          ack_policy: 'explicit',
          max_deliver: 3,
        }
      }
    },
    {
      streamName: 'FRONTEND_MESSAGES' as NatsStreamName,
      subjectName: 'frontend.updates' as NatsSubject,
      processor: createNatsToGraphqlBridge(AppEventType.natsStreamMessage),
      options: {
        maxMessages: 100,
        consumerConfig: {
          durable_name: 'frontend-updates-consumer',
          deliver_policy: 'new',
          ack_policy: 'explicit',
          max_deliver: 3,
        }
      }
    },
    {
      streamName: 'FRONTEND_MESSAGES' as NatsStreamName,
      subjectName: 'frontend.alerts' as NatsSubject,
      processor: createNatsToGraphqlBridge(AppEventType.natsStreamMessage),
      options: {
        maxMessages: 50,
        consumerConfig: {
          durable_name: 'frontend-alerts-consumer',
          deliver_policy: 'new',
          ack_policy: 'explicit',
          max_deliver: 5,
        }
      }
    }
  ]
}

/**
 * Alternative plugin for development/testing that just logs messages
 */
export const loggingOnlyPlugin: NatsServicePlugin = {
  streams: [
    {
      config: {
        name: 'TEST_MESSAGES',
        subjects: ['test.>'],
        retention: 'limits',
        max_msgs: 100,
        max_age: 60 * 60 * 1000000000, // 1 hour
        storage: 'memory',
      }
    }
  ],

  subscriptions: [
    {
      streamName: 'TEST_MESSAGES' as NatsStreamName,
      subjectName: 'test.frontend' as NatsSubject,
      processor: createSimpleNatsProcessor('TEST'),
      options: {
        maxMessages: 10,
        consumerConfig: {
          durable_name: 'test-consumer',
          deliver_policy: 'new',
          ack_policy: 'explicit',
        }
      }
    }
  ]
}
