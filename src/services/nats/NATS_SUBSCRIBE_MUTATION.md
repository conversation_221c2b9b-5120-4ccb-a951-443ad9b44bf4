# NATS Subscribe via GraphQL Mutation

This guide shows how to call the NATS `subscribe` function directly from the client side using GraphQL mutations, similar to how you call queries and mutations.

## Overview

Instead of using GraphQL subscriptions, you can now create NATS subscriptions directly using GraphQL mutations. This gives you more control over when and how NATS subscriptions are created.

## GraphQL Mutations

### 1. Create NATS Subscription

```graphql
mutation CreateNatsSubscription($input: NatsSubscribeInput!) {
  natsSubscribe(input: $input) {
    success
    message
    subscriptionId
    streamName
    subject
    consumerName
  }
}
```

**Input Type:**
```graphql
input NatsSubscribeInput {
  streamName: String!
  subject: String
  consumerName: String!
  bridgeToGraphQL: Boolean = true
  options: NatsSubscribeOptionsInput
}

input NatsSubscribeOptionsInput {
  maxMessages: Int
  expires: Int
  idleHeartbeat: Int
  consumerConfig: NatsConsumerConfigInput
}

input NatsConsumerConfigInput {
  durable_name: String
  deliver_policy: String
  ack_policy: String
  max_deliver: Int
  ack_wait: Int
  replay_policy: String
}
```

### 2. Publish NATS Message

```graphql
mutation PublishNatsMessage($subject: String!, $message: String!, $headers: String) {
  natsPublish(subject: $subject, message: $message, headers: $headers) {
    success
    message
    sequence
    stream
  }
}
```

## Usage Examples

### JavaScript/TypeScript

```typescript
// Apollo Client example
import { useMutation, gql } from '@apollo/client'

const NATS_SUBSCRIBE_MUTATION = gql`
  mutation CreateNatsSubscription($input: NatsSubscribeInput!) {
    natsSubscribe(input: $input) {
      success
      message
      subscriptionId
      streamName
      subject
      consumerName
    }
  }
`

function MyComponent() {
  const [natsSubscribe, { loading, data, error }] = useMutation(NATS_SUBSCRIBE_MUTATION)

  const createSubscription = async () => {
    const input = {
      streamName: "FRONTEND_MESSAGES",
      subject: "frontend.notifications",
      consumerName: "my-consumer",
      bridgeToGraphQL: true,
      options: {
        maxMessages: 100,
        consumerConfig: {
          durable_name: "my-durable-consumer",
          deliver_policy: "new",
          ack_policy: "explicit"
        }
      }
    }

    try {
      const result = await natsSubscribe({ variables: { input } })
      if (result.data?.natsSubscribe?.success) {
        console.log('Subscription created:', result.data.natsSubscribe.subscriptionId)
      }
    } catch (error) {
      console.error('Error creating subscription:', error)
    }
  }

  return (
    <button onClick={createSubscription} disabled={loading}>
      {loading ? 'Creating...' : 'Create NATS Subscription'}
    </button>
  )
}
```

### Vanilla JavaScript

```javascript
async function createNatsSubscription() {
  const query = `
    mutation CreateNatsSubscription($input: NatsSubscribeInput!) {
      natsSubscribe(input: $input) {
        success
        message
        subscriptionId
        streamName
        subject
        consumerName
      }
    }
  `

  const input = {
    streamName: "FRONTEND_MESSAGES",
    subject: "frontend.notifications",
    consumerName: "frontend-consumer-1",
    bridgeToGraphQL: true,
    options: {
      maxMessages: 100,
      consumerConfig: {
        durable_name: "frontend-durable",
        deliver_policy: "new",
        ack_policy: "explicit"
      }
    }
  }

  try {
    const response = await fetch('/fsdata/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        variables: { input }
      })
    })

    const result = await response.json()
    
    if (result.data?.natsSubscribe?.success) {
      console.log('✓ Subscription created successfully!')
      console.log('Subscription ID:', result.data.natsSubscribe.subscriptionId)
    } else {
      console.error('✗ Error:', result.data?.natsSubscribe?.message)
    }
  } catch (error) {
    console.error('Network error:', error)
  }
}
```

### Publishing Messages

```javascript
async function publishNatsMessage() {
  const query = `
    mutation PublishNatsMessage($subject: String!, $message: String!, $headers: String) {
      natsPublish(subject: $subject, message: $message, headers: $headers) {
        success
        message
        sequence
        stream
      }
    }
  `

  const variables = {
    subject: "frontend.notifications",
    message: JSON.stringify({
      type: "notification",
      title: "Test Message",
      content: "Hello from GraphQL!",
      timestamp: new Date().toISOString()
    }),
    headers: JSON.stringify({
      priority: "high",
      source: "frontend"
    })
  }

  const response = await fetch('/fsdata/api/graphql', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ query, variables })
  })

  const result = await response.json()
  console.log('Publish result:', result.data?.natsPublish)
}
```

## Key Features

### 1. Bridge to GraphQL Subscriptions
When `bridgeToGraphQL: true` (default), messages received by the NATS subscription will be automatically forwarded to GraphQL subscriptions, allowing frontend clients to receive them in real-time.

### 2. Flexible Configuration
You can configure all NATS subscription options:
- **Stream Name**: Which NATS stream to subscribe to
- **Subject**: Specific subject pattern (optional)
- **Consumer Name**: Unique consumer identifier
- **Max Messages**: Limit concurrent messages
- **Durable Consumer**: Persistent consumer configuration
- **Delivery Policy**: How to deliver messages (new, all, last, etc.)
- **Acknowledgment Policy**: How to handle message acknowledgments

### 3. Error Handling
The mutation returns detailed success/error information:
```typescript
interface NatsSubscribeResult {
  success: boolean
  message?: string
  subscriptionId?: string
  streamName?: string
  subject?: string
  consumerName?: string
}
```

## Testing

### 1. Use the HTML Example
Open `http://localhost:8092/fsdata/nats-subscribe-mutation-example.html` to test the mutations with a web interface.

### 2. GraphQL Playground
Use GraphQL Playground at `http://localhost:8092/fsdata/api/graphql` to test mutations directly.

### 3. Example Mutations

**Create a subscription:**
```graphql
mutation {
  natsSubscribe(input: {
    streamName: "FRONTEND_MESSAGES"
    subject: "frontend.notifications"
    consumerName: "test-consumer"
    bridgeToGraphQL: true
    options: {
      maxMessages: 50
      consumerConfig: {
        durable_name: "test-durable"
        deliver_policy: "new"
        ack_policy: "explicit"
      }
    }
  }) {
    success
    message
    subscriptionId
  }
}
```

**Publish a test message:**
```graphql
mutation {
  natsPublish(
    subject: "frontend.notifications"
    message: "{\"type\":\"test\",\"content\":\"Hello World\"}"
    headers: "{\"priority\":\"high\"}"
  ) {
    success
    message
    sequence
  }
}
```

## Rate Limiting

- **Subscribe**: 10 requests per minute
- **Publish**: 100 requests per minute

## Best Practices

1. **Use Durable Consumers**: For production, always use durable consumers to ensure message delivery
2. **Unique Consumer Names**: Use unique consumer names to avoid conflicts
3. **Error Handling**: Always check the `success` field in the response
4. **Bridge to GraphQL**: Enable `bridgeToGraphQL` to receive messages via GraphQL subscriptions
5. **Proper Subject Patterns**: Use specific subject patterns to avoid unnecessary message processing

## Combining with GraphQL Subscriptions

After creating a NATS subscription with `bridgeToGraphQL: true`, you can receive the messages using the GraphQL subscription:

```graphql
subscription {
  natsStreamMessages(
    streamName: "FRONTEND_MESSAGES"
    subjectPattern: "frontend\\.notifications"
  ) {
    id
    subject
    message
    data
    natsMetadata {
      sequence
      timestamp
      streamName
    }
    receivedAt
  }
}
```

This gives you the best of both worlds: control over when subscriptions are created (via mutations) and real-time message delivery (via subscriptions).
