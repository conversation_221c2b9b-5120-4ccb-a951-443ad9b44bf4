<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NATS Messages Frontend</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .message {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
            font-family: monospace;
        }
        .error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #d32f2f;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .connected {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }
        .disconnected {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #d32f2f;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976d2;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        #messages {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #fafafa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>NATS Messages via GraphQL Subscriptions</h1>
        
        <div class="controls">
            <h3>Connection Controls</h3>
            <input type="text" id="subjectPattern" placeholder="Subject pattern (regex, optional)" value=".*">
            <button id="connectBtn">Connect</button>
            <button id="disconnectBtn" disabled>Disconnect</button>
            <button id="clearBtn">Clear Messages</button>
        </div>

        <div id="status" class="status disconnected">Disconnected</div>

        <h3>Messages</h3>
        <div id="messages"></div>
    </div>

    <script>
        class NatsGraphQLClient {
            constructor() {
                this.ws = null;
                this.subscriptionId = null;
                this.isConnected = false;
            }

            connect(subjectPattern = '.*') {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/fsdata/api/graphql`;
                
                this.ws = new WebSocket(wsUrl, 'graphql-ws');
                
                this.ws.onopen = () => {
                    console.log('WebSocket connected');
                    this.sendMessage({
                        type: 'connection_init',
                        payload: {}
                    });
                };

                this.ws.onmessage = (event) => {
                    const message = JSON.parse(event.data);
                    this.handleMessage(message, subjectPattern);
                };

                this.ws.onclose = () => {
                    console.log('WebSocket disconnected');
                    this.isConnected = false;
                    this.updateStatus('Disconnected', false);
                };

                this.ws.onerror = (error) => {
                    console.error('WebSocket error:', error);
                    this.addMessage('WebSocket error: ' + error.message, true);
                };
            }

            handleMessage(message, subjectPattern) {
                switch (message.type) {
                    case 'connection_ack':
                        console.log('Connection acknowledged');
                        this.isConnected = true;
                        this.updateStatus('Connected', true);
                        this.subscribe(subjectPattern);
                        break;
                    
                    case 'next':
                        if (message.payload && message.payload.data && message.payload.data.natsMessages) {
                            this.handleNatsMessage(message.payload.data.natsMessages);
                        }
                        break;
                    
                    case 'error':
                        console.error('GraphQL error:', message.payload);
                        this.addMessage('Error: ' + JSON.stringify(message.payload), true);
                        break;
                    
                    case 'complete':
                        console.log('Subscription completed');
                        break;
                }
            }

            subscribe(subjectPattern) {
                this.subscriptionId = this.generateId();
                const subscription = {
                    id: this.subscriptionId,
                    type: 'start',
                    payload: {
                        query: `
                            subscription NatsMessages($subjectPattern: String) {
                                natsMessages(subjectPattern: $subjectPattern) {
                                    message
                                    subject
                                    data
                                    natsMetadata {
                                        subject
                                        sequence
                                        timestamp
                                        streamName
                                    }
                                }
                            }
                        `,
                        variables: {
                            subjectPattern: subjectPattern || null
                        }
                    }
                };
                
                this.sendMessage(subscription);
            }

            handleNatsMessage(natsMessage) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message';
                messageDiv.innerHTML = `
                    <strong>Subject:</strong> ${natsMessage.subject}<br>
                    <strong>Sequence:</strong> ${natsMessage.natsMetadata.sequence}<br>
                    <strong>Timestamp:</strong> ${natsMessage.natsMetadata.timestamp}<br>
                    <strong>Message:</strong> ${natsMessage.message}<br>
                    ${natsMessage.data ? `<strong>Data:</strong> ${natsMessage.data}` : ''}
                `;
                
                const messagesDiv = document.getElementById('messages');
                messagesDiv.appendChild(messageDiv);
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            }

            sendMessage(message) {
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    this.ws.send(JSON.stringify(message));
                }
            }

            disconnect() {
                if (this.subscriptionId) {
                    this.sendMessage({
                        id: this.subscriptionId,
                        type: 'stop'
                    });
                }
                
                if (this.ws) {
                    this.ws.close();
                }
                
                this.isConnected = false;
                this.updateStatus('Disconnected', false);
            }

            generateId() {
                return Math.random().toString(36).substr(2, 9);
            }

            updateStatus(text, connected) {
                const statusDiv = document.getElementById('status');
                statusDiv.textContent = text;
                statusDiv.className = `status ${connected ? 'connected' : 'disconnected'}`;
                
                document.getElementById('connectBtn').disabled = connected;
                document.getElementById('disconnectBtn').disabled = !connected;
            }

            addMessage(text, isError = false) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${isError ? 'error' : ''}`;
                messageDiv.textContent = text;
                
                const messagesDiv = document.getElementById('messages');
                messagesDiv.appendChild(messageDiv);
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            }
        }

        // Initialize the client
        const client = new NatsGraphQLClient();

        // Event listeners
        document.getElementById('connectBtn').addEventListener('click', () => {
            const subjectPattern = document.getElementById('subjectPattern').value;
            client.connect(subjectPattern);
        });

        document.getElementById('disconnectBtn').addEventListener('click', () => {
            client.disconnect();
        });

        document.getElementById('clearBtn').addEventListener('click', () => {
            document.getElementById('messages').innerHTML = '';
        });
    </script>
</body>
</html>
