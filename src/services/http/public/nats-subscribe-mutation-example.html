<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NATS Subscribe via GraphQL Mutation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976d2;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: none;
            background: #f8f9fa;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            background: white;
            border-bottom-color: #2196f3;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>NATS Operations via GraphQL</h1>
        <p>Call NATS subscribe and publish functions directly through GraphQL mutations</p>
        
        <div class="tabs">
            <button class="tab active" onclick="showTab('subscribe')">Subscribe</button>
            <button class="tab" onclick="showTab('publish')">Publish</button>
        </div>

        <!-- Subscribe Tab -->
        <div id="subscribe" class="tab-content active">
            <h3>Create NATS Subscription</h3>
            <form id="subscribeForm">
                <div class="form-group">
                    <label for="streamName">Stream Name:</label>
                    <input type="text" id="streamName" value="FRONTEND_MESSAGES" required>
                </div>
                
                <div class="form-group">
                    <label for="subject">Subject (optional):</label>
                    <input type="text" id="subject" value="frontend.notifications" placeholder="e.g., frontend.notifications">
                </div>
                
                <div class="form-group">
                    <label for="consumerName">Consumer Name:</label>
                    <input type="text" id="consumerName" value="frontend-consumer-1" required>
                </div>
                
                <div class="form-group">
                    <label for="bridgeToGraphQL">Bridge to GraphQL Subscriptions:</label>
                    <select id="bridgeToGraphQL">
                        <option value="true">Yes (recommended)</option>
                        <option value="false">No (just log messages)</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="maxMessages">Max Messages (optional):</label>
                    <input type="number" id="maxMessages" value="100" placeholder="100">
                </div>
                
                <div class="form-group">
                    <label for="durableName">Durable Name (optional):</label>
                    <input type="text" id="durableName" value="frontend-durable" placeholder="frontend-durable">
                </div>
                
                <button type="submit">Create Subscription</button>
            </form>
            
            <div id="subscribeResult" class="result" style="display: none;"></div>
        </div>

        <!-- Publish Tab -->
        <div id="publish" class="tab-content">
            <h3>Publish NATS Message</h3>
            <form id="publishForm">
                <div class="form-group">
                    <label for="publishSubject">Subject:</label>
                    <input type="text" id="publishSubject" value="frontend.notifications" required>
                </div>
                
                <div class="form-group">
                    <label for="publishMessage">Message (JSON or plain text):</label>
                    <textarea id="publishMessage" rows="6" required>{
  "type": "notification",
  "title": "Test Message",
  "content": "This is a test message from GraphQL",
  "timestamp": "${new Date().toISOString()}",
  "userId": "test-user"
}</textarea>
                </div>
                
                <div class="form-group">
                    <label for="publishHeaders">Headers (JSON, optional):</label>
                    <textarea id="publishHeaders" rows="3" placeholder='{"priority": "high", "source": "frontend"}'></textarea>
                </div>
                
                <button type="submit">Publish Message</button>
            </form>
            
            <div id="publishResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <div class="container">
        <h3>Example GraphQL Queries</h3>
        <div class="result">
# Subscribe Mutation
mutation CreateNatsSubscription {
  natsSubscribe(input: {
    streamName: "FRONTEND_MESSAGES"
    subject: "frontend.notifications"
    consumerName: "frontend-consumer-1"
    bridgeToGraphQL: true
    options: {
      maxMessages: 100
      consumerConfig: {
        durable_name: "frontend-durable"
        deliver_policy: "new"
        ack_policy: "explicit"
      }
    }
  }) {
    success
    message
    subscriptionId
    streamName
    subject
    consumerName
  }
}

# Publish Mutation
mutation PublishNatsMessage {
  natsPublish(
    subject: "frontend.notifications"
    message: "{\"type\":\"test\",\"content\":\"Hello World\"}"
    headers: "{\"priority\":\"high\"}"
  ) {
    success
    message
    sequence
    stream
  }
}
        </div>
    </div>

    <script>
        // Tab functionality
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        // GraphQL request helper
        async function graphqlRequest(query, variables = {}) {
            const response = await fetch('/fsdata/api/graphql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    query,
                    variables
                })
            });
            
            return response.json();
        }

        // Subscribe form handler
        document.getElementById('subscribeForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const input = {
                streamName: document.getElementById('streamName').value,
                subject: document.getElementById('subject').value || undefined,
                consumerName: document.getElementById('consumerName').value,
                bridgeToGraphQL: document.getElementById('bridgeToGraphQL').value === 'true',
                options: {
                    maxMessages: parseInt(document.getElementById('maxMessages').value) || undefined,
                    consumerConfig: {
                        durable_name: document.getElementById('durableName').value || undefined,
                        deliver_policy: "new",
                        ack_policy: "explicit"
                    }
                }
            };

            const query = `
                mutation CreateNatsSubscription($input: NatsSubscribeInput!) {
                    natsSubscribe(input: $input) {
                        success
                        message
                        subscriptionId
                        streamName
                        subject
                        consumerName
                    }
                }
            `;

            try {
                const result = await graphqlRequest(query, { input });
                const resultDiv = document.getElementById('subscribeResult');
                resultDiv.style.display = 'block';
                
                if (result.data?.natsSubscribe?.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✓ Subscription created successfully!
Subscription ID: ${result.data.natsSubscribe.subscriptionId}
Stream: ${result.data.natsSubscribe.streamName}
Subject: ${result.data.natsSubscribe.subject || 'All subjects'}
Consumer: ${result.data.natsSubscribe.consumerName}

${result.data.natsSubscribe.message}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `✗ Error: ${result.data?.natsSubscribe?.message || 'Unknown error'}`;
                }
            } catch (error) {
                const resultDiv = document.getElementById('subscribeResult');
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.textContent = `✗ Network error: ${error.message}`;
            }
        });

        // Publish form handler
        document.getElementById('publishForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const subject = document.getElementById('publishSubject').value;
            const message = document.getElementById('publishMessage').value;
            const headers = document.getElementById('publishHeaders').value || undefined;

            const query = `
                mutation PublishNatsMessage($subject: String!, $message: String!, $headers: String) {
                    natsPublish(subject: $subject, message: $message, headers: $headers) {
                        success
                        message
                        sequence
                        stream
                    }
                }
            `;

            try {
                const result = await graphqlRequest(query, { subject, message, headers });
                const resultDiv = document.getElementById('publishResult');
                resultDiv.style.display = 'block';
                
                if (result.data?.natsPublish?.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✓ Message published successfully!
Subject: ${subject}
Sequence: ${result.data.natsPublish.sequence}
Stream: ${result.data.natsPublish.stream}

${result.data.natsPublish.message}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `✗ Error: ${result.data?.natsPublish?.message || 'Unknown error'}`;
                }
            } catch (error) {
                const resultDiv = document.getElementById('publishResult');
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.textContent = `✗ Network error: ${error.message}`;
            }
        });

        // Set current timestamp in message template
        document.addEventListener('DOMContentLoaded', () => {
            const messageTextarea = document.getElementById('publishMessage');
            messageTextarea.value = messageTextarea.value.replace('${new Date().toISOString()}', new Date().toISOString());
        });
    </script>
</body>
</html>
