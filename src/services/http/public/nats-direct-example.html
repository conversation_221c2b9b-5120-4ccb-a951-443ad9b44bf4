<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct NATS WebSocket Connection</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .message {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
            font-family: monospace;
        }
        .error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #d32f2f;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .connected {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }
        .disconnected {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #d32f2f;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976d2;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        #messages {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #fafafa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Direct NATS WebSocket Connection</h1>
        
        <div class="warning">
            <strong>⚠️ Warning:</strong> This approach requires NATS server to be configured with WebSocket support 
            and proper CORS settings. It's more complex than the GraphQL subscription approach and requires 
            additional security considerations for production use.
        </div>

        <div class="controls">
            <h3>Connection Settings</h3>
            <input type="text" id="natsUrl" placeholder="NATS WebSocket URL" value="ws://localhost:8080">
            <input type="text" id="subject" placeholder="Subject to subscribe" value="frontend.>">
            <br>
            <button id="connectBtn">Connect to NATS</button>
            <button id="disconnectBtn" disabled>Disconnect</button>
            <button id="publishBtn" disabled>Publish Test Message</button>
            <button id="clearBtn">Clear Messages</button>
        </div>

        <div id="status" class="status disconnected">Disconnected</div>

        <h3>Messages</h3>
        <div id="messages"></div>

        <h3>Setup Instructions</h3>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px;">
            <p><strong>To use direct NATS WebSocket connection, you need to:</strong></p>
            <ol>
                <li>Configure NATS server with WebSocket support:
                    <pre style="background: #e9ecef; padding: 10px; border-radius: 3px; margin: 10px 0;">
# nats-server.conf
websocket {
  port: 8080
  no_tls: true
}

# Start NATS with WebSocket support
nats-server --config nats-server.conf</pre>
                </li>
                <li>Include NATS WebSocket client library:
                    <pre style="background: #e9ecef; padding: 10px; border-radius: 3px; margin: 10px 0;">
&lt;script src="https://unpkg.com/nats.ws@latest/lib/nats.js"&gt;&lt;/script&gt;</pre>
                </li>
                <li>Handle CORS and security properly for production</li>
            </ol>
            <p><strong>Note:</strong> This example is for demonstration. The GraphQL subscription approach is recommended for most use cases.</p>
        </div>
    </div>

    <!-- NATS WebSocket client library -->
    <script src="https://unpkg.com/nats.ws@latest/lib/nats.js"></script>
    
    <script>
        class DirectNatsClient {
            constructor() {
                this.nc = null;
                this.subscription = null;
                this.isConnected = false;
            }

            async connect(url) {
                try {
                    // Check if NATS library is available
                    if (typeof nats === 'undefined') {
                        throw new Error('NATS WebSocket library not loaded. Please include the NATS.ws library.');
                    }

                    this.addMessage('Connecting to NATS server...');
                    
                    // Connect to NATS server
                    this.nc = await nats.connect({
                        servers: [url],
                        reconnect: true,
                        maxReconnectAttempts: 5,
                        reconnectTimeWait: 2000,
                    });

                    this.isConnected = true;
                    this.updateStatus('Connected to NATS', true);
                    this.addMessage('✓ Connected to NATS server');

                    // Handle connection events
                    this.nc.closed().then(() => {
                        this.isConnected = false;
                        this.updateStatus('Disconnected', false);
                        this.addMessage('Connection closed');
                    });

                } catch (error) {
                    this.addMessage(`Connection error: ${error.message}`, true);
                    this.updateStatus('Connection failed', false);
                    console.error('NATS connection error:', error);
                }
            }

            async subscribe(subject) {
                if (!this.nc || !this.isConnected) {
                    this.addMessage('Not connected to NATS server', true);
                    return;
                }

                try {
                    this.addMessage(`Subscribing to subject: ${subject}`);
                    
                    // Create subscription
                    this.subscription = this.nc.subscribe(subject);
                    
                    // Process messages
                    (async () => {
                        for await (const msg of this.subscription) {
                            this.handleMessage(msg);
                        }
                    })();

                    this.addMessage(`✓ Subscribed to ${subject}`);

                } catch (error) {
                    this.addMessage(`Subscription error: ${error.message}`, true);
                    console.error('NATS subscription error:', error);
                }
            }

            handleMessage(msg) {
                const data = new TextDecoder().decode(msg.data);
                
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message';
                messageDiv.innerHTML = `
                    <strong>Subject:</strong> ${msg.subject}<br>
                    <strong>Data:</strong> ${data}<br>
                    <strong>Reply:</strong> ${msg.reply || 'N/A'}<br>
                    <strong>Timestamp:</strong> ${new Date().toISOString()}
                `;
                
                const messagesDiv = document.getElementById('messages');
                messagesDiv.appendChild(messageDiv);
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            }

            async publish(subject, data) {
                if (!this.nc || !this.isConnected) {
                    this.addMessage('Not connected to NATS server', true);
                    return;
                }

                try {
                    const message = {
                        id: Math.random().toString(36).substr(2, 9),
                        content: data,
                        timestamp: new Date().toISOString(),
                        source: 'frontend-direct'
                    };

                    this.nc.publish(subject, JSON.stringify(message));
                    this.addMessage(`✓ Published to ${subject}: ${data}`);

                } catch (error) {
                    this.addMessage(`Publish error: ${error.message}`, true);
                    console.error('NATS publish error:', error);
                }
            }

            async disconnect() {
                if (this.subscription) {
                    this.subscription.unsubscribe();
                    this.subscription = null;
                }

                if (this.nc) {
                    await this.nc.close();
                    this.nc = null;
                }

                this.isConnected = false;
                this.updateStatus('Disconnected', false);
                this.addMessage('Disconnected from NATS');
            }

            updateStatus(text, connected) {
                const statusDiv = document.getElementById('status');
                statusDiv.textContent = text;
                statusDiv.className = `status ${connected ? 'connected' : 'disconnected'}`;
                
                document.getElementById('connectBtn').disabled = connected;
                document.getElementById('disconnectBtn').disabled = !connected;
                document.getElementById('publishBtn').disabled = !connected;
            }

            addMessage(text, isError = false) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${isError ? 'error' : ''}`;
                messageDiv.textContent = `[${new Date().toLocaleTimeString()}] ${text}`;
                
                const messagesDiv = document.getElementById('messages');
                messagesDiv.appendChild(messageDiv);
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            }
        }

        // Initialize the client
        const client = new DirectNatsClient();

        // Event listeners
        document.getElementById('connectBtn').addEventListener('click', async () => {
            const url = document.getElementById('natsUrl').value;
            await client.connect(url);
            
            if (client.isConnected) {
                const subject = document.getElementById('subject').value;
                await client.subscribe(subject);
            }
        });

        document.getElementById('disconnectBtn').addEventListener('click', async () => {
            await client.disconnect();
        });

        document.getElementById('publishBtn').addEventListener('click', async () => {
            const subject = 'frontend.test';
            const message = `Test message from browser at ${new Date().toISOString()}`;
            await client.publish(subject, message);
        });

        document.getElementById('clearBtn').addEventListener('click', () => {
            document.getElementById('messages').innerHTML = '';
        });

        // Show initial message
        client.addMessage('Ready to connect. Configure NATS server with WebSocket support first.');
    </script>
</body>
</html>
