import { Arg, Directive, Field, ObjectType, Resolver, Root, Subscription } from 'type-graphql'
import { AppEventType } from '../../../app/types/AppEventType.js'
import { BaseGraphQlResolver } from './BaseGraphQlResolver.js'

@ObjectType()
export class NatsMetadata {
  @Field()
  subject!: string

  @Field()
  sequence!: number

  @Field()
  timestamp!: string

  @Field({ nullable: true })
  streamName?: string
}

@ObjectType()
export class NatsMessage {
  @Field()
  message!: string

  @Field()
  subject!: string

  @Field()
  natsMetadata!: NatsMetadata

  @Field({ nullable: true })
  data?: string
}

@Resolver()
export class NatsMessageResolver extends BaseGraphQlResolver {
  
  @Subscription(returns => NatsMessage, {
    topics: AppEventType.objectChanged,
    filter: ({ payload, args }) => {
      if (!payload || !payload.natsMetadata) {
        return false
      }

      // Filter by subject pattern if provided
      if (args.subjectPattern) {
        const regex = new RegExp(args.subjectPattern)
        return regex.test(payload.natsMetadata.subject)
      }

      return true
    },
  })
  @Directive('@rateLimit(limit: 100, duration: 60)')
  async natsMessages(
    @Arg('subjectPattern', { nullable: true }) subjectPattern?: string,
    @Root() payload?: any
  ): Promise<NatsMessage> {
    return {
      message: payload.message || JSON.stringify(payload),
      subject: payload.natsMetadata.subject,
      natsMetadata: payload.natsMetadata,
      data: typeof payload === 'object' ? JSON.stringify(payload) : payload
    }
  }
}
