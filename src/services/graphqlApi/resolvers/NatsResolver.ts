import { 
  Arg, 
  Directive, 
  Field, 
  InputType, 
  Mutation, 
  ObjectType, 
  Resolver 
} from 'type-graphql'

import { BaseGraphQlResolver } from '../../../app/types/services.js'
import { NatsStreamName } from '../../../app/types/NatsStreamName.js'
import { NatsSubject } from '../../../app/types/NatsSubject.js'
import { INatsService } from '../../nats/types/INatsService.js'
import { ServiceName } from '../../../app/types/enums.js'
import appHelpers from '../../../app/helpers.js'
import logger from '../../logger/index.js'
import { createNatsToGraphqlBridge } from '../../nats/helpers/natsToGraphqlBridge.js'
import { AppEventType } from '../../../app/types/AppEventType.js'

// Input types for GraphQL
@InputType()
export class NatsConsumerConfigInput {
  @Field({ nullable: true })
  durable_name?: string

  @Field({ nullable: true })
  deliver_policy?: string

  @Field({ nullable: true })
  ack_policy?: string

  @Field({ nullable: true })
  max_deliver?: number

  @Field({ nullable: true })
  ack_wait?: number

  @Field({ nullable: true })
  replay_policy?: string
}

@InputType()
export class NatsSubscribeOptionsInput {
  @Field({ nullable: true })
  maxMessages?: number

  @Field({ nullable: true })
  expires?: number

  @Field({ nullable: true })
  idleHeartbeat?: number

  @Field({ nullable: true })
  consumerConfig?: NatsConsumerConfigInput
}

@InputType()
export class NatsSubscribeInput {
  @Field()
  streamName!: string

  @Field({ nullable: true })
  subject?: string

  @Field()
  consumerName!: string

  @Field({ nullable: true })
  options?: NatsSubscribeOptionsInput

  @Field({ nullable: true, defaultValue: true })
  bridgeToGraphQL?: boolean
}

// Output types
@ObjectType()
export class NatsSubscribeResult {
  @Field()
  success!: boolean

  @Field({ nullable: true })
  message?: string

  @Field({ nullable: true })
  subscriptionId?: string

  @Field({ nullable: true })
  streamName?: string

  @Field({ nullable: true })
  subject?: string

  @Field({ nullable: true })
  consumerName?: string
}

@ObjectType()
export class NatsPublishResult {
  @Field()
  success!: boolean

  @Field({ nullable: true })
  message?: string

  @Field({ nullable: true })
  sequence?: number

  @Field({ nullable: true })
  stream?: string
}

@Resolver()
export class NatsResolver extends BaseGraphQlResolver {

  @Mutation(returns => NatsSubscribeResult)
  @Directive('@rateLimit(limit: 10, duration: 60)')
  async natsSubscribe(
    @Arg('input') input: NatsSubscribeInput
  ): Promise<NatsSubscribeResult> {
    try {
      const natsService = appHelpers.getService<INatsService>(ServiceName.nats)
      
      if (!natsService) {
        return {
          success: false,
          message: 'NATS service not available'
        }
      }

      // Generate a unique subscription ID
      const subscriptionId = `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      // Create the message processor
      const processor = input.bridgeToGraphQL 
        ? createNatsToGraphqlBridge(AppEventType.natsStreamMessage)
        : async (message: any) => {
            // Simple processor that just logs the message
            const data = new TextDecoder().decode(message.data)
            logger.info('NATS message received via GraphQL subscription', {
              subject: message.subject,
              data: data.substring(0, 200),
              sequence: message.seq,
              subscriptionId
            })
            message.ack()
          }

      // Convert options
      const subscribeOptions = input.options ? {
        maxMessages: input.options.maxMessages,
        expires: input.options.expires,
        idleHeartbeat: input.options.idleHeartbeat,
        consumerConfig: input.options.consumerConfig ? {
          durable_name: input.options.consumerConfig.durable_name,
          deliver_policy: input.options.consumerConfig.deliver_policy as any,
          ack_policy: input.options.consumerConfig.ack_policy as any,
          max_deliver: input.options.consumerConfig.max_deliver,
          ack_wait: input.options.consumerConfig.ack_wait,
          replay_policy: input.options.consumerConfig.replay_policy as any,
        } : undefined
      } : undefined

      // Call the NATS subscribe function
      await natsService.subscribe(
        input.streamName as NatsStreamName,
        input.subject as NatsSubject,
        input.consumerName,
        processor,
        subscribeOptions
      )

      logger.info('NATS subscription created via GraphQL', {
        streamName: input.streamName,
        subject: input.subject,
        consumerName: input.consumerName,
        subscriptionId,
        bridgeToGraphQL: input.bridgeToGraphQL
      })

      return {
        success: true,
        message: 'NATS subscription created successfully',
        subscriptionId,
        streamName: input.streamName,
        subject: input.subject,
        consumerName: input.consumerName
      }

    } catch (error) {
      logger.error('Error creating NATS subscription via GraphQL', {
        error,
        input
      })

      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  @Mutation(returns => NatsPublishResult)
  @Directive('@rateLimit(limit: 100, duration: 60)')
  async natsPublish(
    @Arg('subject') subject: string,
    @Arg('message') message: string,
    @Arg('headers', { nullable: true }) headers?: string
  ): Promise<NatsPublishResult> {
    try {
      const natsService = appHelpers.getService<INatsService>(ServiceName.nats)
      
      if (!natsService) {
        return {
          success: false,
          message: 'NATS service not available'
        }
      }

      // Parse message data
      let messageData: any
      try {
        messageData = JSON.parse(message)
      } catch {
        messageData = message
      }

      // Parse headers if provided
      let parsedHeaders: any = undefined
      if (headers) {
        try {
          parsedHeaders = JSON.parse(headers)
        } catch {
          // Ignore invalid headers
        }
      }

      return new Promise((resolve) => {
        natsService.publish(
          subject,
          messageData,
          parsedHeaders ? { headers: parsedHeaders } : undefined,
          (error, ack) => {
            if (error) {
              resolve({
                success: false,
                message: error.message
              })
            } else {
              resolve({
                success: true,
                message: 'Message published successfully',
                sequence: ack?.seq,
                stream: ack?.stream
              })
            }
          }
        )
      })

    } catch (error) {
      logger.error('Error publishing NATS message via GraphQL', {
        error,
        subject,
        message
      })

      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }
}
